'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { validateMedicineForm, validateField, formatValidationErrors, type ValidationError } from '@/lib/medicine-validation';

interface Category {
  id: number;
  name: string;
}

interface Supplier {
  id: number;
  name: string;
}

interface ApiResponse {
  success: boolean;
  data?: Category[] | Supplier[];
  message?: string;
}

export interface MedicineFormData {
  name: string;
  generic_name: string;
  description: string;
  barcode: string;

  category_id: number;
  supplier_id?: number;
  manufacturer: string;
  approval_number: string;
  specification: string;
  dosage_form: string;
  price: number;
  cost_price: number;
  stock_quantity: number;
  min_stock_level: number;
  is_prescription: boolean;
  is_medical_insurance: boolean;
  storage_condition: string;
  status: 'active' | 'inactive';
}

interface MedicineFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: MedicineFormData) => Promise<void>;
  initialData?: MedicineFormData | null;
  title: string;
}

export default function MedicineForm({ isOpen, onClose, onSubmit, initialData, title }: MedicineFormProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<MedicineFormData>({
    name: '',
    generic_name: '',
    description: '',
    barcode: '',

    category_id: 0,
    supplier_id: undefined,
    manufacturer: '',
    approval_number: '',
    specification: '',
    dosage_form: '',
    price: 0,
    cost_price: 0,
    stock_quantity: 0,
    min_stock_level: 5,
    is_prescription: false,
    is_medical_insurance: false,
    storage_condition: '常温',
    status: 'active',
  });

  // 当 initialData 变化时更新 formData
  useEffect(() => {
    // 如果initialData存在，用它初始化表单
    if (initialData) {
      setFormData({
        name: initialData.name || '',
        generic_name: initialData.generic_name || '',
        description: initialData.description || '',
        barcode: initialData.barcode || '',

        category_id: initialData.category_id || 0,
        supplier_id: initialData.supplier_id || undefined,
        manufacturer: initialData.manufacturer || '',
        approval_number: initialData.approval_number || '',
        specification: initialData.specification || '',
        dosage_form: initialData.dosage_form || '',
        price: initialData.price || 0,
        cost_price: initialData.cost_price || 0,
        stock_quantity: initialData.stock_quantity || 0,
        min_stock_level: initialData.min_stock_level || 5,
        is_prescription: initialData.is_prescription || false,
        is_medical_insurance: initialData.is_medical_insurance || false,
        storage_condition: initialData.storage_condition || '常温',
        status: initialData.status || 'active',
      });
    } else {
      // 如果没有initialData，重置表单
      setFormData({
        name: '',
        generic_name: '',
        description: '',
        barcode: '',

        category_id: 0,
        supplier_id: undefined,
        manufacturer: '',
        approval_number: '',
        specification: '',
        dosage_form: '',
        price: 0,
        cost_price: 0,
        stock_quantity: 0,
        min_stock_level: 5,
        is_prescription: false,
        is_medical_insurance: false,
        storage_condition: '常温',
        status: 'active',
      });
    }
  }, [initialData]);

  useEffect(() => {
    // 获取药品分类数据
    async function fetchCategories() {
      try {
        const response = await fetch('/api/categories');
        const data = await response.json() as ApiResponse;
        if (data.success && data.data) {
          setCategories(data.data as Category[]);
        }
      } catch (error) {
        console.error('获取分类数据失败:', error);
      }
    }

    // 获取供应商数据
    async function fetchSuppliers() {
      try {
        const response = await fetch('/api/suppliers');
        const data = await response.json() as ApiResponse;
        if (data.success && data.data) {
          setSuppliers(data.data as Supplier[]);
        }
      } catch (error) {
        console.error('获取供应商数据失败:', error);
      }
    }

    fetchCategories();
    fetchSuppliers();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const target = e.target;
    const name = target.name as keyof MedicineFormData;
    const value = target.value;
    const type = target.type;

    let newValue: any;

    // 特殊处理category_id
    if (name === 'category_id') {
      newValue = value ? parseInt(value, 10) : 0;
    }
    // 特殊处理supplier_id
    else if (name === 'supplier_id') {
      newValue = value ? parseInt(value, 10) : undefined;
    }
    // 处理其他字段
    else {
      newValue = type === 'checkbox'
        ? (target as HTMLInputElement).checked
        : name.includes('price') || name.includes('quantity') || name.includes('level')
          ? parseFloat(value) || 0
          : value;
    }

    // 更新表单数据
    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    // 清除该字段的验证错误
    setValidationErrors(prev => prev.filter(error => error.field !== name));

    // 实时验证该字段
    const updatedData = { ...formData, [name]: newValue };
    const fieldError = validateField(name, newValue);
    if (fieldError) {
      setValidationErrors(prev => [...prev.filter(error => error.field !== name), fieldError]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 防止重复提交
    if (isSubmitting) return;

    // 执行完整的表单验证
    const validationResult = validateMedicineForm(formData);

    if (!validationResult.isValid) {
      // 显示验证错误
      setValidationErrors(validationResult.errors);

      // 创建错误消息并通过父组件的错误处理机制显示
      const errorMessage = formatValidationErrors(validationResult.errors);

      // 创建一个临时的错误事件来触发父组件的错误处理
      const errorEvent = new Error(errorMessage);
      throw errorEvent;
    }

    // 清除验证错误
    setValidationErrors([]);
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
    } catch (error) {
      // 重新抛出错误让父组件处理
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // 获取字段的错误信息
  const getFieldError = (fieldName: keyof MedicineFormData): string | null => {
    const error = validationErrors.find(err => err.field === fieldName);
    return error ? error.message : null;
  };

  // 获取字段的CSS类名
  const getFieldClassName = (fieldName: keyof MedicineFormData): string => {
    const baseClass = "mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-gray-50 text-gray-900";
    const errorClass = getFieldError(fieldName) ? 'border-red-500' : 'border-gray-300';
    return `${baseClass} ${errorClass}`;
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full bg-gray-50 rounded-xl shadow-lg border border-gray-200">
          <div className="p-6">
            <Dialog.Title className="text-xl font-medium leading-6 text-gray-800 mb-6 border-b pb-3">
              {title}
            </Dialog.Title>

            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="grid grid-cols-2 gap-5">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">药品名称 *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={getFieldClassName('name')}
                  />
                  {getFieldError('name') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('name')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">通用名 *</label>
                  <input
                    type="text"
                    name="generic_name"
                    value={formData.generic_name}
                    onChange={handleChange}
                    className={getFieldClassName('generic_name')}
                  />
                  {getFieldError('generic_name') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('generic_name')}</p>
                  )}
                </div>



                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">条形码</label>
                  <input
                    type="text"
                    name="barcode"
                    value={formData.barcode}
                    onChange={handleChange}
                    className={getFieldClassName('barcode')}
                    placeholder="8-18位数字"
                  />
                  {getFieldError('barcode') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('barcode')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">药品分类 *</label>
                  <select
                    name="category_id"
                    value={formData.category_id || ''}
                    onChange={handleChange}
                    className={getFieldClassName('category_id')}
                  >
                    <option value="" disabled>请选择分类</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                  {getFieldError('category_id') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('category_id')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">供应商</label>
                  <select
                    name="supplier_id"
                    value={formData.supplier_id || ''}
                    onChange={handleChange}
                    className={getFieldClassName('supplier_id')}
                  >
                    <option value="">请选择供应商（可选）</option>
                    {suppliers.map(supplier => (
                      <option key={supplier.id} value={supplier.id}>{supplier.name}</option>
                    ))}
                  </select>
                  {getFieldError('supplier_id') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('supplier_id')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">生产厂家 *</label>
                  <input
                    type="text"
                    name="manufacturer"
                    value={formData.manufacturer}
                    onChange={handleChange}
                    className={getFieldClassName('manufacturer')}
                  />
                  {getFieldError('manufacturer') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('manufacturer')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">批准文号 *</label>
                  <input
                    type="text"
                    name="approval_number"
                    value={formData.approval_number}
                    onChange={handleChange}
                    className={getFieldClassName('approval_number')}
                    placeholder="如：*********"
                  />
                  {getFieldError('approval_number') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('approval_number')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">规格 *</label>
                  <input
                    type="text"
                    name="specification"
                    value={formData.specification}
                    onChange={handleChange}
                    className={getFieldClassName('specification')}
                  />
                  {getFieldError('specification') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('specification')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">剂型 *</label>
                  <input
                    type="text"
                    name="dosage_form"
                    value={formData.dosage_form}
                    onChange={handleChange}
                    className={getFieldClassName('dosage_form')}
                    placeholder="如：片剂、胶囊、注射液等"
                  />
                  {getFieldError('dosage_form') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('dosage_form')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">销售价格 *</label>
                  <input
                    type="number"
                    name="price"
                    value={formData.price}
                    onChange={handleChange}
                    step="0.01"
                    min="0"
                    className={getFieldClassName('price')}
                    placeholder="0.00"
                  />
                  {getFieldError('price') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('price')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">成本价格 *</label>
                  <input
                    type="number"
                    name="cost_price"
                    value={formData.cost_price}
                    onChange={handleChange}
                    step="0.01"
                    min="0"
                    className={getFieldClassName('cost_price')}
                    placeholder="0.00"
                  />
                  {getFieldError('cost_price') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('cost_price')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">库存数量 *</label>
                  <input
                    type="number"
                    name="stock_quantity"
                    value={formData.stock_quantity}
                    onChange={handleChange}
                    min="0"
                    className={getFieldClassName('stock_quantity')}
                    placeholder="0"
                  />
                  {getFieldError('stock_quantity') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('stock_quantity')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">最低库存 *</label>
                  <input
                    type="number"
                    name="min_stock_level"
                    value={formData.min_stock_level}
                    onChange={handleChange}
                    min="0"
                    className={getFieldClassName('min_stock_level')}
                    placeholder="5"
                  />
                  {getFieldError('min_stock_level') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('min_stock_level')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">储存条件 *</label>
                  <select
                    name="storage_condition"
                    value={formData.storage_condition}
                    onChange={handleChange}
                    className={getFieldClassName('storage_condition')}
                  >
                    <option value="常温">常温</option>
                    <option value="阴凉">阴凉</option>
                    <option value="冷藏">冷藏（2-8℃）</option>
                    <option value="冷冻">冷冻（-20℃以下）</option>
                  </select>
                  {getFieldError('storage_condition') && (
                    <p className="mt-1 text-sm text-red-600">{getFieldError('storage_condition')}</p>
                  )}
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="is_prescription"
                      checked={formData.is_prescription}
                      onChange={handleChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-700">处方药</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="is_medical_insurance"
                      checked={formData.is_medical_insurance}
                      onChange={handleChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-700">医保</label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">药品状态</label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    className={getFieldClassName('status')}
                  >
                    <option value="active">在售</option>
                    <option value="inactive">下架</option>
                  </select>
                </div>
              </div>

              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">药品描述</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className={getFieldClassName('description')}
                  placeholder="请输入药品描述信息（可选）"
                />
                {getFieldError('description') && (
                  <p className="mt-1 text-sm text-red-600">{getFieldError('description')}</p>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  disabled={isSubmitting}
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-5 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? '保存中...' : '保存'}
                </button>
              </div>
            </form>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}